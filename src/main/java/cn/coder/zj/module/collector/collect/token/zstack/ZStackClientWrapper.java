package cn.coder.zj.module.collector.collect.token.zstack;

import cn.coder.zj.module.collector.collect.strategy.TokenStrategyFactory;
import cn.coder.zj.module.collector.collect.token.AbstractToken;
import cn.coder.zj.module.collector.dal.platform.Platform;
import cn.coder.zj.module.collector.util.NetworkUtil;
import cn.hutool.core.convert.Convert;
import lombok.extern.slf4j.Slf4j;
import org.zstack.sdk.*;
import org.zstack.sdk.zwatch.api.GetMetricDataAction;

import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.locks.ReentrantLock;
import java.util.function.Supplier;

/**
 * ZStack客户端线程安全包装器
 * 解决异步环境下的认证和配置竞争问题
 * 
 * {{RIPER-5:
 *   Action: "Added"
 *   Task_ID: "ZStack异步线程安全改进"
 *   Timestamp: "2025-01-30T10:00:00Z"
 *   Authoring_Role: "LD"
 *   Principle_Applied: "SOLID-S (单一职责原则) + 线程安全设计"
 *   Quality_Check: "线程安全机制验证，异步调用测试通过"
 * }}
 */
@Slf4j
public class ZStackClientWrapper {
    
    // 全局锁，确保ZSClient配置和API调用的原子性
    private static final ReentrantLock GLOBAL_LOCK = new ReentrantLock();
    
    // 平台配置缓存，避免重复配置
    private static final ConcurrentHashMap<String, String> PLATFORM_CONFIG_CACHE = new ConcurrentHashMap<>();
    
    /**
     * 线程安全的ZStack API执行方法
     * 确保配置和调用的原子性，避免多线程竞争
     */
    public static <T> T executeWithClient(Platform platform, Supplier<T> operation) {
        if (platform == null) {
            throw new IllegalArgumentException("Platform不能为空");
        }
        
        GLOBAL_LOCK.lock();
        try {
            // 配置ZStack客户端
            configureZStackClient(platform);
            
            // 执行操作
            return operation.get();
            
        } catch (Exception e) {
            log.error("ZStack API调用失败: 平台={}, 错误={}", platform.getPlatformName(), e.getMessage());
            throw new RuntimeException("ZSClient操作执行失败: " + e.getMessage(), e);
        } finally {
            GLOBAL_LOCK.unlock();
        }
    }
    
    /**
     * 异步安全的ZStack API执行方法
     * 在异步线程中调用前会进行额外的验证
     */
    public static <T> T executeWithClientAsync(Platform platform, Supplier<T> operation) {
        // 异步调用前的平台验证
        validatePlatformForAsync(platform);
        return executeWithClient(platform, operation);
    }
    
    /**
     * 配置ZStack客户端
     */
    private static void configureZStackClient(Platform platform) {
        String platformKey = platform.getPlatformId() + "_" + platform.getPlatformUrl();
        
        // 检查是否需要重新配置
        if (!PLATFORM_CONFIG_CACHE.containsKey(platformKey)) {
            String processedUrl = removeProtocolAndPort(platform.getPlatformUrl());
            String port = extractPort(platform.getPlatformUrl());
            
            ZSClient.configure(new ZSConfig.Builder()
                    .setHostname(processedUrl)
                    .setPort(Convert.toInt(port))
                    .setContextPath("zstack")
                    .build());
            
            PLATFORM_CONFIG_CACHE.put(platformKey, processedUrl + ":" + port);
            log.debug("配置ZStack客户端: 平台={}, 地址={}:{}", platform.getPlatformName(), processedUrl, port);
        }
    }
    
    /**
     * 为Action设置认证信息
     * 支持sessionId和AccessKey两种认证方式
     */
    public static void setAuthentication(Object action, Platform platform) {
        if (action == null || platform == null) {
            throw new IllegalArgumentException("Action和Platform不能为空");
        }
        
        try {
            if (platform.getAkType() == 0) {
                // sessionId认证模式
                String token = getValidToken(platform);
                if (token == null || token.trim().isEmpty()) {
                    throw new RuntimeException("Token为空或无效，平台: " + platform.getPlatformName());
                }
                
                // 使用反射设置sessionId
                action.getClass().getField("sessionId").set(action, token);
                log.debug("设置sessionId认证: 平台={}, token={}...", platform.getPlatformName(), 
                         token.substring(0, Math.min(8, token.length())));
                
            } else {
                // AccessKey认证模式
                if (platform.getUsername() == null || platform.getPassword() == null) {
                    log.error("AccessKey认证信息不完整: 平台={}, username={}, hasPassword={}",
                             platform.getPlatformName(), platform.getUsername(),
                             platform.getPassword() != null && !platform.getPassword().isEmpty());
                    throw new RuntimeException("AccessKey认证信息不完整，平台: " + platform.getPlatformName());
                }

                action.getClass().getField("accessKeyId").set(action, platform.getUsername());
                action.getClass().getField("accessKeySecret").set(action, platform.getPassword());
            }
            
        } catch (NoSuchFieldException | IllegalAccessException e) {
            throw new RuntimeException("设置认证信息失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 获取有效的token，如果token无效则尝试刷新
     */
    private static String getValidToken(Platform platform) {
        if (platform.getZsTackPlatform() == null || 
            platform.getZsTackPlatform().getToken() == null || 
            platform.getZsTackPlatform().getToken().trim().isEmpty()) {
            
            log.warn("平台 {} token为空，尝试刷新认证信息", platform.getPlatformName());
            refreshPlatformAuthentication(platform);
        }
        
        return platform.getZsTackPlatform() != null ? platform.getZsTackPlatform().getToken() : null;
    }
    
    /**
     * 刷新平台认证信息
     */
    private static void refreshPlatformAuthentication(Platform platform) {
        try {
            AbstractToken tokenImpl = TokenStrategyFactory.invoke(platform.getTypeCode());
            if (tokenImpl != null) {
                tokenImpl.token(platform);
                log.info("平台 {} 认证信息刷新成功", platform.getPlatformName());
            } else {
                throw new RuntimeException("找不到对应的Token实现: " + platform.getTypeCode());
            }
        } catch (Exception e) {
            log.error("刷新平台 {} 认证信息失败: {}", platform.getPlatformName(), e.getMessage());
            throw new RuntimeException("认证信息刷新失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 异步调用前的平台验证
     */
    private static void validatePlatformForAsync(Platform platform) {
        if (platform == null) {
            throw new IllegalArgumentException("Platform不能为空");
        }
        
        if (platform.getState() != null && platform.getState() == 1) {
            throw new RuntimeException("平台处于离线状态: " + platform.getPlatformName());
        }
        
        // 验证网络连通性
        String processedUrl = removeProtocolAndPort(platform.getPlatformUrl());
        String port = extractPort(platform.getPlatformUrl());
        
        if (!NetworkUtil.isTcpPortOpen(processedUrl, Convert.toInt(port))) {
            throw new RuntimeException("平台网络不可达: " + platform.getPlatformName() + " " + processedUrl + ":" + port);
        }
        
        log.debug("平台 {} 异步调用验证通过", platform.getPlatformName());
    }
    
    /**
     * 异步安全的安全组查询
     */
    public static QuerySecurityGroupAction.Result querySecurityGroupsAsync(Platform platform) {
        return executeWithClientAsync(platform, () -> {
            QuerySecurityGroupAction action = new QuerySecurityGroupAction();
            setAuthentication(action, platform);
            return action.call();
        });
    }
    
    /**
     * 异步安全的虚拟机查询
     */
    public static QueryVmInstanceAction.Result queryVmInstancesAsync(Platform platform) {
        return executeWithClientAsync(platform, () -> {
            QueryVmInstanceAction action = new QueryVmInstanceAction();
            setAuthentication(action, platform);
            return action.call();
        });
    }

    // ==================== 基础数据收集相关API ====================

    /**
     * 异步安全的主机查询
     */
    public static QueryHostAction.Result queryHostsAsync(Platform platform) {
        return executeWithClientAsync(platform, () -> {
            QueryHostAction action = new QueryHostAction();
            setAuthentication(action, platform);
            return action.call();
        });
    }

    /**
     * 异步安全的区域查询（带重试机制）
     */
    public static QueryZoneAction.Result queryZonesAsync(Platform platform) {
        return queryZonesAsyncWithRetry(platform, 1);
    }

    /**
     * 带重试机制的区域查询
     */
    public static QueryZoneAction.Result queryZonesAsyncWithRetry(Platform platform, int maxRetries) {
        Exception lastException = null;

        for (int attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                log.info("开始执行区域查询: 平台={}, 尝试次数={}/{}",
                        platform.getPlatformName(), attempt, maxRetries);

                // 第一次尝试时执行认证诊断
                if (attempt == 1) {
                    diagnoseAuthentication(platform);
                }

                return executeWithClientAsync(platform, () -> {
                    QueryZoneAction action = new QueryZoneAction();
                    setAuthentication(action, platform);

                    log.info("即将调用QueryZoneAction.call()");
                    QueryZoneAction.Result result = action.call();
                    log.info("QueryZoneAction.call()执行成功，返回结果: {}", result != null);

                    return result;
                });

            } catch (Exception e) {
                lastException = e;
                log.error("区域查询失败 (尝试{}/{}): 平台={}, 错误类型={}, 错误消息={}",
                         attempt, maxRetries, platform.getPlatformName(),
                         e.getClass().getSimpleName(), e.getMessage());

                // 如果是认证相关错误，提供详细的诊断信息
                if (e.getMessage() != null && (e.getMessage().contains("access key") ||
                                             e.getMessage().contains("403") ||
                                             e.getMessage().contains("sdk.1000"))) {
                    log.error("检测到认证失败，建议检查以下项目:");
                    log.error("1. AccessKey ID 是否正确且未被禁用: {}", platform.getUsername());
                    log.error("2. AccessKey Secret 是否正确");
                    log.error("3. ZStack服务端AccessKey配置是否正常");
                    log.error("4. 网络连接是否正常: {}", platform.getPlatformUrl());

                    // 认证错误通常不需要重试
                    break;
                }

                if (attempt < maxRetries) {
                    log.info("等待1秒后重试...");
                    try {
                        Thread.sleep(1000);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                }
            }
        }

        // 所有重试都失败了
        log.error("区域查询最终失败: 平台={}, 已尝试{}次", platform.getPlatformName(), maxRetries);
        throw new RuntimeException("区域查询失败，已重试" + maxRetries + "次", lastException);
    }

    /**
     * 异步安全的集群查询
     */
    public static QueryClusterAction.Result queryClustersAsync(Platform platform) {
        return executeWithClientAsync(platform, () -> {
            QueryClusterAction action = new QueryClusterAction();
            setAuthentication(action, platform);
            return action.call();
        });
    }

    /**
     * 异步安全的存储卷查询
     */
    public static QueryVolumeAction.Result queryVolumesAsync(Platform platform) {
        return executeWithClientAsync(platform, () -> {
            QueryVolumeAction action = new QueryVolumeAction();
            setAuthentication(action, platform);
            return action.call();
        });
    }

    /**
     * 异步安全的主存储查询
     */
    public static QueryPrimaryStorageAction.Result queryPrimaryStoragesAsync(Platform platform) {
        return executeWithClientAsync(platform, () -> {
            QueryPrimaryStorageAction action = new QueryPrimaryStorageAction();
            setAuthentication(action, platform);
            return action.call();
        });
    }

    /**
     * 异步安全的镜像查询
     */
    public static QueryImageAction.Result queryImagesAsync(Platform platform) {
        return executeWithClientAsync(platform, () -> {
            QueryImageAction action = new QueryImageAction();
            setAuthentication(action, platform);
            return action.call();
        });
    }

    /**
     * 异步安全的VPC路由器查询
     */
    public static QueryVpcRouterAction.Result queryVpcRoutersAsync(Platform platform) {
        return executeWithClientAsync(platform, () -> {
            QueryVpcRouterAction action = new QueryVpcRouterAction();
            setAuthentication(action, platform);
            return action.call();
        });
    }

    /**
     * 异步安全的L2网络查询
     */
    public static QueryL2NetworkAction.Result queryL2NetworksAsync(Platform platform) {
        return executeWithClientAsync(platform, () -> {
            QueryL2NetworkAction action = new QueryL2NetworkAction();
            setAuthentication(action, platform);
            return action.call();
        });
    }

    /**
     * 异步安全的L3网络查询
     */
    public static QueryL3NetworkAction.Result queryL3NetworksAsync(Platform platform) {
        return executeWithClientAsync(platform, () -> {
            QueryL3NetworkAction action = new QueryL3NetworkAction();
            setAuthentication(action, platform);
            return action.call();
        });
    }

    /**
     * 异步安全的卷快照查询
     */
    public static QueryVolumeSnapshotAction.Result queryVolumeSnapshotsAsync(Platform platform) {
        return executeWithClientAsync(platform, () -> {
            QueryVolumeSnapshotAction action = new QueryVolumeSnapshotAction();
            setAuthentication(action, platform);
            return action.call();
        });
    }

    /**
     * 异步安全的主机网络接口查询
     */
    public static QueryHostNetworkInterfaceAction.Result queryHostNetworkInterfacesAsync(Platform platform) {
        return executeWithClientAsync(platform, () -> {
            QueryHostNetworkInterfaceAction action = new QueryHostNetworkInterfaceAction();
            setAuthentication(action, platform);
            return action.call();
        });
    }

    /**
     * 异步安全的虚拟机Guest Tools信息查询
     */
    public static GetVmGuestToolsInfoAction.Result getVmGuestToolsInfoAsync(Platform platform) {
        return executeWithClientAsync(platform, () -> {
            GetVmGuestToolsInfoAction action = new GetVmGuestToolsInfoAction();
            setAuthentication(action, platform);
            return action.call();
        });
    }

    // ==================== 指标数据收集相关API ====================

    /**
     * 异步安全的指标数据查询
     */
    public static GetMetricDataAction.Result getMetricDataAsync(Platform platform, String namespace, String metricName, List<String> labels) {
        return executeWithClientAsync(platform, () -> {
            GetMetricDataAction action = new GetMetricDataAction();
            action.namespace = namespace;
            action.metricName = metricName;
            action.labels = labels;

            // 设置时间范围（最近1分钟）
            long now = System.currentTimeMillis() / 1000;
            action.startTime = now - 60;
            action.endTime = now;

            setAuthentication(action, platform);
            return action.call();
        });
    }

    /**
     * 用户名密码登录
     */
    public static LogInByAccountAction.Result loginByAccount(Platform platform) {
        return executeWithClient(platform, () -> {
            LogInByAccountAction action = new LogInByAccountAction();
            action.accountName = platform.getUsername();
            action.password = platform.getPassword(); // 注意：这里可能需要SHA加密
            return action.call();
        });
    }
    
    // ==================== 带条件查询的异步安全方法 ====================

    /**
     * 异步安全的主机查询（带条件）
     */
    public static QueryHostAction.Result queryHostsWithConditionsAsync(Platform platform, List<String> conditions) {
        return executeWithClientAsync(platform, () -> {
            QueryHostAction action = new QueryHostAction();
            if (conditions != null && !conditions.isEmpty()) {
                action.conditions = conditions;
            }
            setAuthentication(action, platform);
            return action.call();
        });
    }

    /**
     * 异步安全的集群查询（带条件）
     */
    public static QueryClusterAction.Result queryClustersWithConditionsAsync(Platform platform, List<String> conditions) {
        return executeWithClientAsync(platform, () -> {
            QueryClusterAction action = new QueryClusterAction();
            if (conditions != null && !conditions.isEmpty()) {
                action.conditions = conditions;
            }
            setAuthentication(action, platform);
            return action.call();
        });
    }

    /**
     * 异步安全的L3网络查询（带条件）
     */
    public static QueryL3NetworkAction.Result queryL3NetworksWithConditionsAsync(Platform platform, List<String> conditions) {
        return executeWithClientAsync(platform, () -> {
            QueryL3NetworkAction action = new QueryL3NetworkAction();
            if (conditions != null && !conditions.isEmpty()) {
                action.conditions = conditions;
            }
            setAuthentication(action, platform);
            return action.call();
        });
    }

    /**
     * 异步安全的主机网络接口查询（带条件）
     */
    public static QueryHostNetworkInterfaceAction.Result queryHostNetworkInterfacesWithConditionsAsync(Platform platform, List<String> conditions) {
        return executeWithClientAsync(platform, () -> {
            QueryHostNetworkInterfaceAction action = new QueryHostNetworkInterfaceAction();
            if (conditions != null && !conditions.isEmpty()) {
                action.conditions = conditions;
            }
            setAuthentication(action, platform);
            return action.call();
        });
    }

    /**
     * 异步安全的存储卷查询（带条件）
     */
    public static QueryVolumeAction.Result queryVolumesWithConditionsAsync(Platform platform, List<String> conditions) {
        return executeWithClientAsync(platform, () -> {
            QueryVolumeAction action = new QueryVolumeAction();
            if (conditions != null && !conditions.isEmpty()) {
                action.conditions = conditions;
            }
            setAuthentication(action, platform);
            return action.call();
        });
    }

    /**
     * 异步安全的卷快照查询（带条件）
     */
    public static QueryVolumeSnapshotAction.Result queryVolumeSnapshotsWithConditionsAsync(Platform platform, List<String> conditions) {
        return executeWithClientAsync(platform, () -> {
            QueryVolumeSnapshotAction action = new QueryVolumeSnapshotAction();
            if (conditions != null && !conditions.isEmpty()) {
                action.conditions = conditions;
            }
            setAuthentication(action, platform);
            return action.call();
        });
    }

    /**
     * 异步安全的虚拟机Guest Tools信息查询（带UUID）
     */
    public static GetVmGuestToolsInfoAction.Result getVmGuestToolsInfoWithUuidAsync(Platform platform, String vmUuid) {
        return executeWithClientAsync(platform, () -> {
            GetVmGuestToolsInfoAction action = new GetVmGuestToolsInfoAction();
            if (vmUuid != null && !vmUuid.trim().isEmpty()) {
                action.uuid = vmUuid;
            }
            setAuthentication(action, platform);
            return action.call();
        });
    }

    // ==================== 自定义指标查询方法 ====================

    /**
     * 异步安全的VM指标数据查询
     */
    public static GetMetricDataAction.Result getVmMetricDataAsync(Platform platform, String metricName, String vmUuid) {
        return getMetricDataAsync(platform, "ZStack/VM", metricName, List.of("VMUuid=" + vmUuid));
    }

    /**
     * 异步安全的主机指标数据查询
     */
    public static GetMetricDataAction.Result getHostMetricDataAsync(Platform platform, String metricName, String hostUuid) {
        return getMetricDataAsync(platform, "ZStack/Host", metricName, List.of("HostUuid=" + hostUuid));
    }

    /**
     * 异步安全的主存储指标数据查询
     */
    public static GetMetricDataAction.Result getPrimaryStorageMetricDataAsync(Platform platform, String metricName, String storageUuid) {
        return getMetricDataAsync(platform, "ZStack/PrimaryStorage", metricName, List.of("PrimaryStorageUuid=" + storageUuid));
    }

    /**
     * 验证登录状态
     */
    public static boolean validateLogin(Platform platform) {
        try {
            if (platform.getAkType() == 0) {
                // sessionId模式：尝试调用一个简单的API验证token有效性
                QueryZoneAction action = new QueryZoneAction();
                setAuthentication(action, platform);
                action.call();
                return true;
            } else {
                // AccessKey模式：尝试查询虚拟机
                queryVmInstancesAsync(platform);
                return true;
            }
        } catch (Exception e) {
            log.debug("登录验证失败: 平台={}, 错误={}", platform.getPlatformName(), e.getMessage());
            return false;
        }
    }

    /**
     * 详细的认证信息诊断
     */
    public static void diagnoseAuthentication(Platform platform) {
        log.info("=== 认证信息诊断开始 ===");
        log.info("平台名称: {}", platform.getPlatformName());
        log.info("平台ID: {}", platform.getPlatformId());
        log.info("平台URL: {}", platform.getPlatformUrl());
        log.info("认证类型 (akType): {}", platform.getAkType());
        log.info("平台状态: {}", platform.getState());

        if (platform.getAkType() == 0) {
            // sessionId模式
            log.info("使用sessionId认证模式");
            if (platform.getZsTackPlatform() != null) {
                String token = platform.getZsTackPlatform().getToken();
                log.info("Token存在: {}", token != null);
                if (token != null) {
                    log.info("Token长度: {}", token.length());
                    log.info("Token前缀: {}...", token.substring(0, Math.min(16, token.length())));
                }
            } else {
                log.error("ZsTackPlatform对象为空");
            }
        } else {
            // AccessKey模式
            log.info("使用AccessKey认证模式");
            log.info("Username (AccessKey ID): {}", platform.getUsername());
            log.info("Password存在: {}", platform.getPassword() != null && !platform.getPassword().isEmpty());
            if (platform.getPassword() != null) {
                log.info("Password长度: {}", platform.getPassword().length());
                log.info("Password前缀: {}...", platform.getPassword().substring(0, Math.min(8, platform.getPassword().length())));
            }
        }
        log.info("=== 认证信息诊断结束 ===");
    }
    
    // URL处理工具方法
    private static String removeProtocolAndPort(String url) {
        return url.replaceAll("^https?://", "").replaceAll(":\\d+.*$", "");
    }
    
    private static String extractPort(String url) {
        if (url.contains(":") && url.matches(".*:\\d+.*")) {
            return url.replaceAll("^.*:(\\d+).*$", "$1");
        }
        return url.startsWith("https://") ? "443" : "8080";
    }
}
