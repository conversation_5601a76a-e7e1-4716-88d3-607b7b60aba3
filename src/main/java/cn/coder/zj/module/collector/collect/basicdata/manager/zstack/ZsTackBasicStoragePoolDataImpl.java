package cn.coder.zj.module.collector.collect.basicdata.manager.zstack;

import cn.coder.zj.module.collector.collect.basicdata.AbstractBasicData;
import cn.coder.zj.module.collector.collect.token.zstack.ZStackClientWrapper;
import cn.coder.zj.module.collector.common.cache.CacheService;
import cn.coder.zj.module.collector.dal.platform.Platform;
import cn.coder.zj.module.collector.enums.PlatformType;
import cn.coder.zj.module.collector.remoting.send.SendMessageService;
import cn.coder.zj.module.collector.util.GsonUtil;
import cn.coder.zj.module.collector.util.SpringBeanUtils;
import cn.hutool.core.convert.Convert;
import cn.iocoder.zj.framework.common.dal.manager.BasicCollectData;
import cn.iocoder.zj.framework.common.dal.manager.StoragePoolData;
import cn.iocoder.zj.framework.common.message.ClusterMsg;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.task.TaskExecutor;
import org.zstack.sdk.CephPrimaryStoragePoolInventory;
import org.zstack.sdk.QueryCephPrimaryStoragePoolAction;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import static cn.iocoder.zj.framework.common.enums.BasicServiceType.BASIC_STORAGE_POOL;
import static cn.iocoder.zj.framework.common.enums.BasicType.BASIC_ZS_TACK_STORAGE_POOL;

@Slf4j
public class ZsTackBasicStoragePoolDataImpl extends AbstractBasicData {
    protected long startTime;

    @Override
    public void preCheck(Platform platform) {

    }

    @Override
    public void collectBasicData(ClusterMsg.Message.Builder message) {
        this.startTime = System.currentTimeMillis();
        List<Object> platformList = CacheService.getValuesByKey1(PlatformType.ZS_TACK.code());
        TaskExecutor taskExecutor = SpringBeanUtils.getBean(TaskExecutor.class);
        SendMessageService sendMessageService = SpringBeanUtils.getBean(SendMessageService.class);
        for (Object platformObj : platformList) {
            Platform platform =(Platform) platformObj;
            if (platform.getState() == 1) {
                continue;
            }

            taskExecutor.execute(() -> {
                List<StoragePoolData> dataList = new ArrayList<>();
                QueryCephPrimaryStoragePoolAction.Result res = ZStackClientWrapper.executeWithClientAsync(platform, () -> {
                    QueryCephPrimaryStoragePoolAction action = new QueryCephPrimaryStoragePoolAction();
                    ZStackClientWrapper.setAuthentication(action, platform);
                    return action.call();
                });
                List<CephPrimaryStoragePoolInventory> inventories1 = res.value.inventories;
                for (CephPrimaryStoragePoolInventory pool : inventories1) {
                    StoragePoolData createRespDTO = new StoragePoolData();
                    createRespDTO.setUuid(pool.getUuid());
                    createRespDTO.setName(pool.getPoolName());
                    createRespDTO.setType(pool.getType());
                    createRespDTO.setDescription(pool.getDescription());
                    createRespDTO.setSecurityPolicy(pool.getSecurityPolicy());
                    createRespDTO.setStorageUuid(pool.getPrimaryStorageUuid());
                    createRespDTO.setAvailableCapacity(new BigDecimal(pool.getAvailableCapacity()));
                    createRespDTO.setUsedCapacity(new BigDecimal(pool.getUsedCapacity()));
                    createRespDTO.setTotalCapacity(new BigDecimal(pool.getTotalCapacity()));
                    createRespDTO.setVCreateDate(Convert.toDate(pool.getCreateDate()));
                    createRespDTO.setLastOpDate(Convert.toDate(pool.getLastOpDate()));
                    createRespDTO.setPlatformId(platform.getPlatformId());
                    createRespDTO.setPlatformName(platform.getPlatformName());
                    dataList.add(createRespDTO);
                }
                if (!dataList.isEmpty()) {
                    BasicCollectData build = BasicCollectData.builder().basicDataMap(dataList)
                            .metricsName(BASIC_STORAGE_POOL.code())
                            .build();
                    String endTimeFormatted = String.format("%.2f", (System.currentTimeMillis() - startTime) / 1000.0);
                    log.info("collect basic data end, cost {} seconds", endTimeFormatted);
                    message.setType(ClusterMsg.MessageType.BASIC);
                    message.setData(GsonUtil.GSON.toJson(build));
                    message.setTime(System.currentTimeMillis());
                    sendMessageService.sendMessage(CacheService.getCtx("ctx"), message.build());
                }
            });
        }

    }


    @Override
    public String supportProtocol() {
        return BASIC_ZS_TACK_STORAGE_POOL.code();
    }
}
