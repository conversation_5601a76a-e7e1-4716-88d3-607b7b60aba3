package cn.coder.zj.module.collector.collect.basicdata.manager.zstack;

import cn.coder.zj.module.collector.collect.basicdata.AbstractBasicData;
import cn.coder.zj.module.collector.collect.token.zstack.ZStackClientWrapper;
import cn.coder.zj.module.collector.common.cache.CacheService;
import cn.coder.zj.module.collector.dal.platform.Platform;
import cn.coder.zj.module.collector.enums.PlatformType;
import cn.coder.zj.module.collector.remoting.send.SendMessageService;
import cn.coder.zj.module.collector.util.GsonUtil;
import cn.coder.zj.module.collector.util.SpringBeanUtils;
import cn.iocoder.zj.framework.common.dal.manager.BasicCollectData;
import cn.iocoder.zj.framework.common.dal.manager.HostNicData;
import cn.iocoder.zj.framework.common.message.ClusterMsg;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.core.task.TaskExecutor;
import org.zstack.sdk.*;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

import static cn.iocoder.zj.framework.common.enums.BasicServiceType.BASIC_HOST_VIC;
import static cn.iocoder.zj.framework.common.enums.BasicType.BASIC_ZS_TACK_HOST_VIC;

@Slf4j
public class ZsTackBasicHostNicDataImpl extends AbstractBasicData {
    protected long startTime;

    private static final String KVM = "KVM";

    @Override
    public void preCheck(Platform platform) {

    }

    @Override
    public void collectBasicData(ClusterMsg.Message.Builder message) {
        this.startTime = System.currentTimeMillis();
        List<Object> platformList = CacheService.getValuesByKey1(PlatformType.ZS_TACK.code());
        TaskExecutor taskExecutor = SpringBeanUtils.getBean(TaskExecutor.class);
        SendMessageService sendMessageService = SpringBeanUtils.getBean(SendMessageService.class);
        for (Object platformObj : platformList) {
            Platform platform = (Platform) platformObj;
            if (platform.getState() == 1) {
                continue;
            }
            taskExecutor.execute(() -> {
                List<HostNicData> list = new ArrayList<>();
                QueryHostAction.Result hostResult = ZStackClientWrapper.queryHostsAsync(platform);

                for (Object host : hostResult.value.inventories) {
                    JsonObject jsonObject = GsonUtil.GSON.toJsonTree(host).getAsJsonObject();
                    JsonElement hypervisorType = jsonObject.get("hypervisorType");
                    if (hypervisorType != null && !hypervisorType.isJsonNull() &&
                            KVM.equals(hypervisorType.getAsString())) {
                        String uuid = jsonObject.get("uuid").getAsString();
                        //三级网络列表
                        QueryL3NetworkAction.Result l3res = ZStackClientWrapper.queryL3NetworksAsync(platform);
                        List<L3NetworkInventory> l3Inventories = l3res.value.inventories;

                        //查询集群下所有二级网络
                        String clusterUuid = jsonObject.get("clusterUuid").getAsString();
                        QueryL2NetworkAction.Result networkRes = ZStackClientWrapper.executeWithClientAsync(platform, () -> {
                            QueryL2NetworkAction action = new QueryL2NetworkAction();
                            action.conditions = Arrays.asList("cluster.uuid=" + clusterUuid);
                            ZStackClientWrapper.setAuthentication(action, platform);
                            return action.call();
                        });
                        List<L2NetworkInventory> l2Inventories = networkRes.value.inventories;

                        //添加网络信息
                        for (L2NetworkInventory l2Inventory : l2Inventories) {
                            for (L3NetworkInventory l3Inventory : l3Inventories) {
                                if (l3Inventory.getL2NetworkUuid().equals(l2Inventory.getUuid())) {
                                    HostNicData hostNicData = new HostNicData();
                                    hostNicData.setUuid(l3Inventory.getUuid());
                                    hostNicData.setHardwareUuid(uuid);
                                    hostNicData.setNetworkType("-");
                                    hostNicData.setIpAddresses("-");
                                    hostNicData.setIpSubnet(l3Inventory.getName());
                                    hostNicData.setL2NetworkUuid(l2Inventory.getUuid());
                                    hostNicData.setL2NetworkName(l2Inventory.getName());
                                    hostNicData.setState(true);
                                    hostNicData.setPlatformId(platform.getPlatformId());
                                    hostNicData.setPlatformName(platform.getPlatformName());
                                    list.add(hostNicData);
                                }
                            }
                        }
                    }
                }
                if (!list.isEmpty()) {
                    BasicCollectData build = BasicCollectData.builder().basicDataMap(list)
                            .metricsName(BASIC_HOST_VIC.code())
                            .build();
                    String endTimeFormatted = String.format("%.2f", (System.currentTimeMillis() - startTime) / 1000.0);
                    log.info("collect basic data end, cost {} seconds", endTimeFormatted);
                    message.setType(ClusterMsg.MessageType.BASIC);
                    message.setData(GsonUtil.GSON.toJson(build));
                    message.setTime(System.currentTimeMillis());
                    sendMessageService.sendMessage(CacheService.getCtx("ctx"), message.build());
                }
            });
        }
    }


    @Override
    public String supportProtocol() {
        return BASIC_ZS_TACK_HOST_VIC.code();
    }
}
