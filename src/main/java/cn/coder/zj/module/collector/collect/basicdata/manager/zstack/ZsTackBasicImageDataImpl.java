package cn.coder.zj.module.collector.collect.basicdata.manager.zstack;

import cn.coder.zj.module.collector.collect.basicdata.AbstractBasicData;
import cn.coder.zj.module.collector.collect.token.zstack.ZStackClientWrapper;
import cn.coder.zj.module.collector.common.cache.CacheService;
import cn.coder.zj.module.collector.dal.platform.Platform;
import cn.coder.zj.module.collector.enums.PlatformType;
import cn.coder.zj.module.collector.remoting.send.SendMessageService;
import cn.coder.zj.module.collector.util.GsonUtil;
import cn.coder.zj.module.collector.util.SpringBeanUtils;
import cn.hutool.core.convert.Convert;
import cn.iocoder.zj.framework.common.dal.manager.BasicCollectData;
import cn.iocoder.zj.framework.common.dal.manager.ImageData;
import cn.iocoder.zj.framework.common.message.ClusterMsg;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.task.TaskExecutor;
import org.zstack.sdk.ImageInventory;
import org.zstack.sdk.QueryImageAction;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import static cn.iocoder.zj.framework.common.enums.BasicServiceType.BASIC_IMAGE;
import static cn.iocoder.zj.framework.common.enums.BasicType.BASIC_ZS_TACK_IMAGE;


@Slf4j
public class ZsTackBasicImageDataImpl extends AbstractBasicData {

    protected long startTime;

    @Override
    public void preCheck(Platform platform) {

    }

    @Override
    public void collectBasicData(ClusterMsg.Message.Builder message) {
        this.startTime = System.currentTimeMillis();
        List<Object> platformList = CacheService.getValuesByKey1(PlatformType.ZS_TACK.code());
        TaskExecutor taskExecutor = SpringBeanUtils.getBean(TaskExecutor.class);
        SendMessageService sendMessageService = SpringBeanUtils.getBean(SendMessageService.class);
        for (Object platformInfo : platformList) {
            Platform platform = (Platform) platformInfo;
            if (platform.getState() == 1) {
                continue;
            }

            taskExecutor.execute(() -> {
                List<ImageData> dataList = new ArrayList<>();

                // 使用线程安全的镜像查询
                QueryImageAction.Result result = ZStackClientWrapper.queryImagesAsync(platform);
                // 解析镜像信息
                List<ImageInventory> images = result.value.getInventories();
                for (ImageInventory image : images) {
                    // 构建镜像信息DTO
                    ImageData imageInfo = new ImageData();
                    imageInfo.setUuid(image.uuid);
                    imageInfo.setName(image.name);
                    imageInfo.setStatus(image.state);
                    imageInfo.setFormat(image.format);
                    imageInfo.setCpuArch(image.architecture);
                    imageInfo.setOsType(image.guestOsType);
                    imageInfo.setSize(image.size);
                    imageInfo.setImageType("ISO".equals(image.mediaType) ? "RootVolumeTemplate" : image.mediaType);
                    imageInfo.setSharingScope("不共享");
                    imageInfo.setVCreateDate(Convert.toDate(image.createDate));
                    imageInfo.setVUpdateDate(Convert.toDate(image.lastOpDate));
                    imageInfo.setOsLanguage("");
                    imageInfo.setMinMemory(null);
                    imageInfo.setMinDisk(BigDecimal.valueOf(image.size));
                    imageInfo.setDiskDriver("");
                    imageInfo.setNetworkDriver("");
                    imageInfo.setBootMode("");
                    imageInfo.setRemoteProtocol("");
                    imageInfo.setApplicationPlatform(image.platform);
                    imageInfo.setPlatformId(platform.getPlatformId());
                    imageInfo.setPlatformName(platform.getPlatformName());
                    dataList.add(imageInfo);
                }
                if (!dataList.isEmpty()) {
                    message.setType(ClusterMsg.MessageType.BASIC);
                    message.setData(GsonUtil.GSON.toJson(BasicCollectData.builder().basicDataMap(dataList)
                            .metricsName(BASIC_IMAGE.code())
                            .build()));
                    message.setTime(System.currentTimeMillis());
                    sendMessageService.sendMessage(CacheService.getCtx("ctx"), message.build());
                }
                String endTimeFormatted = String.format("%.2f", (System.currentTimeMillis() - startTime) / 1000.0);
                log.info("collect basic data end, cost {} seconds", endTimeFormatted);
            });
        }
    }


    @Override
    public String supportProtocol() {
        return BASIC_ZS_TACK_IMAGE.code();
    }
}
